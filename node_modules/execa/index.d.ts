export type {
	StdinOption,
	StdinSyncOption,
	StdoutStderrOption,
	StdoutStderrSyncOption,
} from './types/stdio/type.js';
export type {Options, SyncOptions} from './types/arguments/options.js';
export type {TemplateExpression} from './types/methods/template.js';

export type {Result, SyncResult} from './types/return/result.js';
export type {ResultPromise, Subprocess} from './types/subprocess/subprocess.js';
export {ExecaError, ExecaSyncError} from './types/return/final-error.js';

export {execa, type ExecaMethod} from './types/methods/main-async.js';
export {execaSync, type ExecaSyncMethod} from './types/methods/main-sync.js';
export {execaCommand, execaCommandSync, parseCommandString} from './types/methods/command.js';
export {$, type ExecaScriptMethod, type ExecaScriptSyncMethod} from './types/methods/script.js';
export {execaNode, type ExecaNodeMethod} from './types/methods/node.js';

export {
	sendMessage,
	getOneMessage,
	getEachMessage,
	getCancelSignal,
	type Message,
} from './types/ipc.js';
export type {VerboseObject, SyncVerboseObject} from './types/verbose.js';
