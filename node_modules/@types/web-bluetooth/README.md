# Installation
> `npm install --save @types/web-bluetooth`

# Summary
This package contains type definitions for web-bluetooth (https://webbluetoothcg.github.io/web-bluetooth/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/web-bluetooth.

### Additional Details
 * Last updated: Fri, 28 Feb 2025 23:32:04 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/urish), [<PERSON>](https://github.com/xlozinguez), [<PERSON>](https://github.com/thegecko), [<PERSON>](https://github.com/DaBs), and [<PERSON>](https://github.com/TooTallNate).
