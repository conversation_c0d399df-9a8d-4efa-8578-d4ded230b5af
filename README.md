# MovieStream - Vue.js Movie Streaming App

A modern, Netflix/OSN/Prime-style movie streaming web application built with Vue.js 3, featuring beautiful animations, responsive design, and integrated movie/TV show streaming capabilities.

## 🎬 Features

- **Modern UI Design**: Netflix/OSN/Prime-inspired interface with smooth animations
- **Responsive Navigation**: Fixed navigation bar with search functionality
- **Content Sections**: Home, Movies, and TV Shows pages with organized content sections
- **Movie/TV Details**: Detailed view pages with embedded video players
- **Search Functionality**: Search across movies and TV shows
- **Infinite Scroll**: "View All" sections with unlimited scrolling
- **Video Streaming**: Integrated with autoembed.cc player API
- **Mobile Responsive**: Fully responsive design for all devices
- **Smooth Animations**: Page transitions, hover effects, and loading states

## 🚀 Technology Stack

- **Frontend**: Vue.js 3 with Composition API
- **Build Tool**: Vite
- **Routing**: Vue Router 4
- **State Management**: Pinia
- **HTTP Client**: Axios
- **Styling**: CSS3 with CSS Variables
- **Movie Data**: TMDB API (with fallback mock data)
- **Video Player**: autoembed.cc API

## 📦 Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open in browser**:
   Navigate to `http://localhost:5174`

## 🔧 Configuration

### API Configuration

The app uses your provided API key for the autoembed.cc service:
- **API Key**: `3308647fabe47a844ab269e6eab19132`
- **Movie Player**: `https://player.autoembed.cc/embed/movie/{id}`
- **TV Player**: `https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}`

### TMDB API (Optional)

To get real movie data instead of mock data, add your TMDB API key in `src/services/api.js`:
```javascript
const API_KEY = 'your_tmdb_api_key_here'
```

## 🎯 Usage

### Navigation
- **Home**: Browse trending content, popular movies/TV shows
- **Movies**: Dedicated movies page with multiple sections
- **TV Shows**: TV shows page with seasons and episodes
- **Search**: Search for specific movies or TV shows

### Watching Content
1. Browse or search for content
2. Click on any movie/TV show card
3. View details and select season/episode (for TV shows)
4. Click "Play" to start streaming

### Features
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Smooth Animations**: Hover effects, page transitions
- **Infinite Scroll**: Load more content in sections
- **Search**: Real-time search with results

## 📱 Responsive Design

The app is fully responsive with breakpoints for:
- **Desktop**: 1024px and above
- **Tablet**: 768px to 1023px
- **Mobile**: Below 768px

## 🎨 Design Features

### Color Scheme
- **Primary Background**: Dark theme (#141414)
- **Secondary Background**: Darker shade (#1a1a1a)
- **Accent Colors**: Red (#e50914), Blue (#0073e6), Gold (#ffd700)
- **Text**: White primary, gray secondary

### Animations
- **Page Transitions**: Smooth fade effects
- **Hover Effects**: Scale and shadow animations
- **Loading States**: Spinner animations
- **Navigation**: Smooth mobile menu transitions

## 🔄 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## 📁 Project Structure

```
src/
├── components/          # Reusable Vue components
│   ├── NavBar.vue      # Navigation bar
│   ├── MovieCard.vue   # Movie/TV show card
│   └── ContentSection.vue # Content section with carousel/grid
├── views/              # Page components
│   ├── HomeView.vue    # Home page
│   ├── MoviesView.vue  # Movies page
│   ├── TVShowsView.vue # TV shows page
│   ├── MovieDetailView.vue # Movie details
│   ├── TVDetailView.vue    # TV show details
│   └── SearchView.vue  # Search results
├── services/           # API services
│   └── api.js         # Movie service and player integration
├── router/            # Vue Router configuration
└── assets/           # Styles and assets
```

## 🌟 Key Features Implemented

✅ **Netflix/OSN/Prime-style UI**
✅ **Responsive navigation with animations**
✅ **Content sections with "View All"**
✅ **Infinite scroll functionality**
✅ **Movie/TV detail pages**
✅ **Embedded video player**
✅ **Search functionality**
✅ **Mobile responsive design**
✅ **Smooth animations and transitions**
✅ **Error handling and loading states**

## 🎬 Video Player Integration

The app integrates with autoembed.cc for video streaming:
- Movies: Direct play with movie ID
- TV Shows: Season and episode selection
- Responsive iframe player
- Fullscreen support

## 🚀 Deployment

To deploy the application:

1. **Build for production**:
   ```bash
   npm run build
   ```

2. **Deploy the `dist` folder** to your hosting service

## 📝 Notes

- The app includes mock data for demonstration when TMDB API is not available
- All animations and transitions are optimized for performance
- The design follows modern web standards and accessibility guidelines
- Error handling is implemented throughout the application

---

**Enjoy streaming! 🍿**
