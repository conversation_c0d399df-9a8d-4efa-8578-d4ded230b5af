<template>
  <div class="search-view">
    <div class="search-header">
      <h1 class="search-title">Search Results</h1>
      <div class="search-info" v-if="searchQuery">
        <p>Results for: <strong>"{{ searchQuery }}"</strong></p>
        <p v-if="totalResults > 0">{{ totalResults }} results found</p>
      </div>
    </div>

    <!-- Search Results -->
    <div class="search-results">
      <!-- Loading State -->
      <div v-if="loading && results.length === 0" class="loading-container">
        <div class="spinner"></div>
        <p>Searching...</p>
      </div>

      <!-- No Results -->
      <div v-else-if="!loading && results.length === 0 && searchQuery" class="no-results">
        <h2>No results found</h2>
        <p>Try searching with different keywords</p>
      </div>

      <!-- Results Grid -->
      <div v-else-if="results.length > 0" class="results-container">
        <div class="results-grid">
          <MovieCard
            v-for="item in results"
            :key="`${item.media_type}-${item.id}`"
            :id="item.id"
            :title="item.title || item.name"
            :overview="item.overview"
            :poster-path="item.poster_path"
            :backdrop-path="item.backdrop_path"
            :rating="item.vote_average"
            :release-date="item.release_date || item.first_air_date"
            :media-type="item.media_type"
            :genres="item.genres || []"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMore" class="load-more-container">
          <button 
            class="btn btn-secondary load-more-btn"
            @click="loadMore"
            :disabled="loading"
          >
            <span v-if="loading" class="spinner small"></span>
            {{ loading ? 'Loading...' : 'Load More' }}
          </button>
        </div>
      </div>

      <!-- Default State -->
      <div v-else class="default-state">
        <h2>Search for Movies and TV Shows</h2>
        <p>Use the search bar above to find your favorite content</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MovieCard from '@/components/MovieCard.vue'
import { movieService } from '@/services/api'

export default {
  name: 'SearchView',
  components: {
    MovieCard
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const results = ref([])
    const loading = ref(false)
    const currentPage = ref(1)
    const totalPages = ref(0)
    const totalResults = ref(0)

    const searchQuery = computed(() => route.query.q || '')
    const hasMore = computed(() => currentPage.value < totalPages.value && currentPage.value < 10) // Limit to 10 pages

    const searchContent = async (query, page = 1) => {
      if (!query.trim()) {
        results.value = []
        return
      }

      loading.value = true

      try {
        // Search both movies and TV shows
        const [moviesResponse, tvResponse] = await Promise.all([
          movieService.searchMovies(query, page),
          movieService.searchTVShows(query, page)
        ])

        const movies = moviesResponse.data.results.map(item => ({
          ...item,
          media_type: 'movie'
        }))

        const tvShows = tvResponse.data.results.map(item => ({
          ...item,
          media_type: 'tv'
        }))

        // Combine and sort by popularity
        const combinedResults = [...movies, ...tvShows]
          .sort((a, b) => b.popularity - a.popularity)

        if (page === 1) {
          results.value = combinedResults
        } else {
          results.value.push(...combinedResults)
        }

        // Use the higher total from either movies or TV shows
        const maxTotal = Math.max(moviesResponse.data.total_results, tvResponse.data.total_results)
        const maxPages = Math.max(moviesResponse.data.total_pages, tvResponse.data.total_pages)
        
        totalResults.value = maxTotal
        totalPages.value = maxPages
        currentPage.value = page

      } catch (error) {
        console.error('Error searching content:', error)
        results.value = []
      } finally {
        loading.value = false
      }
    }

    const loadMore = () => {
      if (hasMore.value && !loading.value) {
        searchContent(searchQuery.value, currentPage.value + 1)
      }
    }

    // Watch for query changes
    watch(
      () => route.query.q,
      (newQuery) => {
        if (newQuery !== searchQuery.value) {
          currentPage.value = 1
          searchContent(newQuery)
        }
      },
      { immediate: true }
    )

    onMounted(() => {
      if (searchQuery.value) {
        searchContent(searchQuery.value)
      }
    })

    return {
      results,
      loading,
      searchQuery,
      totalResults,
      hasMore,
      loadMore
    }
  }
}
</script>

<style scoped>
.search-view {
  width: 100%;
  min-height: 100vh;
  padding: 2rem 0;
}

.search-header {
  text-align: center;
  padding: 2rem;
  margin-bottom: 2rem;
}

.search-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  background: linear-gradient(45deg, var(--accent-red), var(--accent-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.search-info {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.search-info strong {
  color: var(--text-primary);
}

.search-results {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.loading-container,
.no-results,
.default-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 40vh;
  text-align: center;
}

.no-results h2,
.default-state h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.no-results p,
.default-state p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.results-container {
  width: 100%;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  font-size: 1rem;
}

.spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .search-view {
    padding: 1rem 0;
  }

  .search-header {
    padding: 1rem;
  }

  .search-title {
    font-size: 2rem;
  }

  .search-results {
    padding: 0 1rem;
  }

  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }

  .load-more-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}
</style>
