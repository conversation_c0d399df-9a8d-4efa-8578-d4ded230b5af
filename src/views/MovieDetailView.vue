<template>
  <div class="movie-detail-view">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading movie details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <h2>Error Loading Movie</h2>
      <p>{{ error }}</p>
      <button class="btn btn-primary" @click="fetchMovieDetails">Try Again</button>
    </div>

    <!-- Movie Details -->
    <div v-else-if="movie" class="movie-details">
      <!-- Hero Section -->
      <section class="hero-section" :style="{ backgroundImage: `url(${backdropUrl})` }">
        <div class="hero-overlay">
          <div class="hero-content">
            <div class="movie-poster">
              <img :src="posterUrl" :alt="movie.title" class="poster-image" />
            </div>
            <div class="movie-info">
              <h1 class="movie-title">{{ movie.title }}</h1>
              <div class="movie-meta">
                <span class="rating">
                  <i class="star-icon">⭐</i>
                  {{ movie.vote_average?.toFixed(1) }}
                </span>
                <span class="year">{{ releaseYear }}</span>
                <span class="runtime" v-if="movie.runtime">{{ movie.runtime }} min</span>
              </div>
              <div class="genres" v-if="movie.genres && movie.genres.length">
                <span v-for="genre in movie.genres" :key="genre.id" class="genre-tag">
                  {{ genre.name }}
                </span>
              </div>
              <p class="movie-overview">{{ movie.overview }}</p>
              <div class="action-buttons">
                <button class="btn btn-primary" @click="playMovie">
                  <i class="play-icon">▶</i>
                  Play Movie
                </button>
                <button class="btn btn-secondary" @click="toggleWatchlist">
                  <i class="heart-icon">♡</i>
                  Add to Watchlist
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Player Section -->
      <section v-if="showPlayer" class="player-section">
        <div class="player-container">
          <iframe
            :src="playerUrl"
            class="player-iframe"
            frameborder="0"
            allowfullscreen
            allow="autoplay; encrypted-media"
          ></iframe>
        </div>
      </section>

      <!-- Additional Info -->
      <section class="additional-info">
        <div class="info-container">
          <div class="info-grid">
            <div class="info-item" v-if="movie.release_date">
              <h3>Release Date</h3>
              <p>{{ formatDate(movie.release_date) }}</p>
            </div>
            <div class="info-item" v-if="movie.budget">
              <h3>Budget</h3>
              <p>${{ formatCurrency(movie.budget) }}</p>
            </div>
            <div class="info-item" v-if="movie.revenue">
              <h3>Revenue</h3>
              <p>${{ formatCurrency(movie.revenue) }}</p>
            </div>
            <div class="info-item" v-if="movie.production_companies && movie.production_companies.length">
              <h3>Production Companies</h3>
              <p>{{ movie.production_companies.map(c => c.name).join(', ') }}</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { movieService, playerService, imageService } from '@/services/api'

export default {
  name: 'MovieDetailView',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const movie = ref(null)
    const loading = ref(false)
    const error = ref('')
    const showPlayer = ref(false)

    const posterUrl = computed(() => {
      if (movie.value?.poster_path) {
        return imageService.getPosterUrl(movie.value.poster_path, 'w500')
      }
      return 'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=500&h=750&fit=crop&crop=center'
    })

    const backdropUrl = computed(() => {
      if (movie.value?.backdrop_path) {
        return imageService.getBackdropUrl(movie.value.backdrop_path, 'w1280')
      }
      return 'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=1280&h=720&fit=crop&crop=center'
    })

    const releaseYear = computed(() => {
      if (!movie.value?.release_date) return ''
      return new Date(movie.value.release_date).getFullYear()
    })

    const playerUrl = computed(() => {
      return playerService.getMoviePlayerUrl(props.id)
    })

    const fetchMovieDetails = async () => {
      loading.value = true
      error.value = ''
      
      try {
        const response = await movieService.getMovieDetails(props.id)
        movie.value = response.data
      } catch (err) {
        error.value = 'Failed to load movie details. Please try again.'
        console.error('Error fetching movie details:', err)
      } finally {
        loading.value = false
      }
    }

    const playMovie = () => {
      showPlayer.value = true
      // Scroll to player
      setTimeout(() => {
        const playerSection = document.querySelector('.player-section')
        if (playerSection) {
          playerSection.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    }

    const toggleWatchlist = () => {
      // TODO: Implement watchlist functionality
      console.log('Add to watchlist clicked')
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-US').format(amount)
    }

    onMounted(() => {
      fetchMovieDetails()
    })

    return {
      movie,
      loading,
      error,
      showPlayer,
      posterUrl,
      backdropUrl,
      releaseYear,
      playerUrl,
      fetchMovieDetails,
      playMovie,
      toggleWatchlist,
      formatDate,
      formatCurrency
    }
  }
}
</script>

<style scoped>
.movie-detail-view {
  width: 100%;
  min-height: 100vh;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
}

.hero-section {
  position: relative;
  min-height: 80vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(20, 20, 20, 0.9) 0%,
    rgba(20, 20, 20, 0.6) 50%,
    transparent 100%
  );
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  gap: 3rem;
  width: 100%;
}

.movie-poster {
  flex-shrink: 0;
}

.poster-image {
  width: 300px;
  height: 450px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.6);
}

.movie-info {
  flex: 1;
  max-width: 600px;
}

.movie-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.1;
}

.movie-meta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.rating,
.year,
.runtime {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
}

.genres {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.genre-tag {
  background-color: var(--accent-red);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.movie-overview {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: var(--text-primary);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.player-section {
  padding: 2rem;
  background-color: var(--primary-bg);
}

.player-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.player-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.additional-info {
  padding: 3rem 2rem;
  background-color: var(--secondary-bg);
}

.info-container {
  max-width: 1200px;
  margin: 0 auto;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.info-item h3 {
  color: var(--accent-red);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.info-item p {
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
    padding: 1rem;
  }

  .poster-image {
    width: 250px;
    height: 375px;
  }

  .movie-title {
    font-size: 2rem;
  }

  .movie-meta {
    justify-content: center;
  }

  .action-buttons {
    justify-content: center;
  }

  .additional-info {
    padding: 2rem 1rem;
  }
}
</style>
