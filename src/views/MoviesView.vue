<template>
  <div class="movies-view">
    <div class="page-header">
      <h1 class="page-title">Movies</h1>
      <p class="page-subtitle">Discover amazing movies from around the world</p>
    </div>

    <div class="content-sections">
      <ContentSection
        title="Popular Movies"
        :items="popularMovies"
        :loading="loading.popular"
        :error="errors.popular"
        :has-more="hasMore.popular"
        :loading-more="loadingMore.popular"
        view-type="grid"
        media-type="movie"
        :show-view-all="false"
        @load-more="loadMorePopular"
        @retry="fetchPopularMovies"
      />

      <ContentSection
        title="Top Rated Movies"
        :items="topRatedMovies"
        :loading="loading.topRated"
        :error="errors.topRated"
        :has-more="hasMore.topRated"
        :loading-more="loadingMore.topRated"
        view-type="grid"
        media-type="movie"
        :show-view-all="false"
        @load-more="loadMoreTopRated"
        @retry="fetchTopRatedMovies"
      />

      <ContentSection
        title="Now Playing"
        :items="nowPlayingMovies"
        :loading="loading.nowPlaying"
        :error="errors.nowPlaying"
        :has-more="hasMore.nowPlaying"
        :loading-more="loadingMore.nowPlaying"
        view-type="grid"
        media-type="movie"
        :show-view-all="false"
        @load-more="loadMoreNowPlaying"
        @retry="fetchNowPlayingMovies"
      />

      <ContentSection
        title="Upcoming Movies"
        :items="upcomingMovies"
        :loading="loading.upcoming"
        :error="errors.upcoming"
        :has-more="hasMore.upcoming"
        :loading-more="loadingMore.upcoming"
        view-type="grid"
        media-type="movie"
        :show-view-all="false"
        @load-more="loadMoreUpcoming"
        @retry="fetchUpcomingMovies"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import ContentSection from '@/components/ContentSection.vue'
import { movieService } from '@/services/api'

export default {
  name: 'MoviesView',
  components: {
    ContentSection
  },
  setup() {
    // Reactive data
    const popularMovies = ref([])
    const topRatedMovies = ref([])
    const nowPlayingMovies = ref([])
    const upcomingMovies = ref([])
    
    // Pagination
    const currentPage = ref({
      popular: 1,
      topRated: 1,
      nowPlaying: 1,
      upcoming: 1
    })
    
    const hasMore = ref({
      popular: true,
      topRated: true,
      nowPlaying: true,
      upcoming: true
    })
    
    // Loading states
    const loading = ref({
      popular: false,
      topRated: false,
      nowPlaying: false,
      upcoming: false
    })
    
    const loadingMore = ref({
      popular: false,
      topRated: false,
      nowPlaying: false,
      upcoming: false
    })
    
    // Error states
    const errors = ref({
      popular: '',
      topRated: '',
      nowPlaying: '',
      upcoming: ''
    })

    // API calls
    const fetchPopularMovies = async (page = 1) => {
      if (page === 1) {
        loading.value.popular = true
        popularMovies.value = []
      } else {
        loadingMore.value.popular = true
      }
      
      errors.value.popular = ''
      
      try {
        const response = await movieService.getPopularMovies(page)
        const newMovies = response.data.results
        
        if (page === 1) {
          popularMovies.value = newMovies
        } else {
          popularMovies.value.push(...newMovies)
        }
        
        hasMore.value.popular = page < response.data.total_pages && page < 10 // Limit to 10 pages
        currentPage.value.popular = page
      } catch (error) {
        errors.value.popular = 'Failed to load popular movies'
        console.error('Error fetching popular movies:', error)
      } finally {
        loading.value.popular = false
        loadingMore.value.popular = false
      }
    }

    const fetchTopRatedMovies = async (page = 1) => {
      if (page === 1) {
        loading.value.topRated = true
        topRatedMovies.value = []
      } else {
        loadingMore.value.topRated = true
      }
      
      errors.value.topRated = ''
      
      try {
        const response = await movieService.getTopRatedMovies(page)
        const newMovies = response.data.results
        
        if (page === 1) {
          topRatedMovies.value = newMovies
        } else {
          topRatedMovies.value.push(...newMovies)
        }
        
        hasMore.value.topRated = page < response.data.total_pages && page < 10
        currentPage.value.topRated = page
      } catch (error) {
        errors.value.topRated = 'Failed to load top rated movies'
        console.error('Error fetching top rated movies:', error)
      } finally {
        loading.value.topRated = false
        loadingMore.value.topRated = false
      }
    }

    const fetchNowPlayingMovies = async (page = 1) => {
      if (page === 1) {
        loading.value.nowPlaying = true
        nowPlayingMovies.value = []
      } else {
        loadingMore.value.nowPlaying = true
      }
      
      errors.value.nowPlaying = ''
      
      try {
        const response = await movieService.getNowPlayingMovies(page)
        const newMovies = response.data.results
        
        if (page === 1) {
          nowPlayingMovies.value = newMovies
        } else {
          nowPlayingMovies.value.push(...newMovies)
        }
        
        hasMore.value.nowPlaying = page < response.data.total_pages && page < 10
        currentPage.value.nowPlaying = page
      } catch (error) {
        errors.value.nowPlaying = 'Failed to load now playing movies'
        console.error('Error fetching now playing movies:', error)
      } finally {
        loading.value.nowPlaying = false
        loadingMore.value.nowPlaying = false
      }
    }

    const fetchUpcomingMovies = async (page = 1) => {
      if (page === 1) {
        loading.value.upcoming = true
        upcomingMovies.value = []
      } else {
        loadingMore.value.upcoming = true
      }
      
      errors.value.upcoming = ''
      
      try {
        const response = await movieService.getUpcomingMovies(page)
        const newMovies = response.data.results
        
        if (page === 1) {
          upcomingMovies.value = newMovies
        } else {
          upcomingMovies.value.push(...newMovies)
        }
        
        hasMore.value.upcoming = page < response.data.total_pages && page < 10
        currentPage.value.upcoming = page
      } catch (error) {
        errors.value.upcoming = 'Failed to load upcoming movies'
        console.error('Error fetching upcoming movies:', error)
      } finally {
        loading.value.upcoming = false
        loadingMore.value.upcoming = false
      }
    }

    // Load more functions
    const loadMorePopular = () => {
      if (hasMore.value.popular && !loadingMore.value.popular) {
        fetchPopularMovies(currentPage.value.popular + 1)
      }
    }

    const loadMoreTopRated = () => {
      if (hasMore.value.topRated && !loadingMore.value.topRated) {
        fetchTopRatedMovies(currentPage.value.topRated + 1)
      }
    }

    const loadMoreNowPlaying = () => {
      if (hasMore.value.nowPlaying && !loadingMore.value.nowPlaying) {
        fetchNowPlayingMovies(currentPage.value.nowPlaying + 1)
      }
    }

    const loadMoreUpcoming = () => {
      if (hasMore.value.upcoming && !loadingMore.value.upcoming) {
        fetchUpcomingMovies(currentPage.value.upcoming + 1)
      }
    }

    // Initialize data
    onMounted(() => {
      fetchPopularMovies()
      fetchTopRatedMovies()
      fetchNowPlayingMovies()
      fetchUpcomingMovies()
    })

    return {
      popularMovies,
      topRatedMovies,
      nowPlayingMovies,
      upcomingMovies,
      loading,
      loadingMore,
      errors,
      hasMore,
      fetchPopularMovies,
      fetchTopRatedMovies,
      fetchNowPlayingMovies,
      fetchUpcomingMovies,
      loadMorePopular,
      loadMoreTopRated,
      loadMoreNowPlaying,
      loadMoreUpcoming
    }
  }
}
</script>

<style scoped>
.movies-view {
  width: 100%;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, var(--primary-bg), var(--secondary-bg));
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  background: linear-gradient(45deg, var(--accent-red), var(--accent-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.content-sections {
  padding: 2rem 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 2rem 1rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }
}
</style>
