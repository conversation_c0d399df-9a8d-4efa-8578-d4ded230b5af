<template>
  <div class="tv-shows-view">
    <div class="page-header">
      <h1 class="page-title">TV Shows</h1>
      <p class="page-subtitle">Explore the best television series and shows</p>
    </div>

    <div class="content-sections">
      <ContentSection
        title="Popular TV Shows"
        :items="popularTVShows"
        :loading="loading.popular"
        :error="errors.popular"
        :has-more="hasMore.popular"
        :loading-more="loadingMore.popular"
        view-type="grid"
        media-type="tv"
        :show-view-all="false"
        @load-more="loadMorePopular"
        @retry="fetchPopularTVShows"
      />

      <ContentSection
        title="Top Rated TV Shows"
        :items="topRatedTVShows"
        :loading="loading.topRated"
        :error="errors.topRated"
        :has-more="hasMore.topRated"
        :loading-more="loadingMore.topRated"
        view-type="grid"
        media-type="tv"
        :show-view-all="false"
        @load-more="loadMoreTopRated"
        @retry="fetchTopRatedTVShows"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import ContentSection from '@/components/ContentSection.vue'
import { movieService } from '@/services/api'

export default {
  name: 'TVShowsView',
  components: {
    ContentSection
  },
  setup() {
    // Reactive data
    const popularTVShows = ref([])
    const topRatedTVShows = ref([])
    
    // Pagination
    const currentPage = ref({
      popular: 1,
      topRated: 1
    })
    
    const hasMore = ref({
      popular: true,
      topRated: true
    })
    
    // Loading states
    const loading = ref({
      popular: false,
      topRated: false
    })
    
    const loadingMore = ref({
      popular: false,
      topRated: false
    })
    
    // Error states
    const errors = ref({
      popular: '',
      topRated: ''
    })

    // API calls
    const fetchPopularTVShows = async (page = 1) => {
      if (page === 1) {
        loading.value.popular = true
        popularTVShows.value = []
      } else {
        loadingMore.value.popular = true
      }
      
      errors.value.popular = ''
      
      try {
        const response = await movieService.getPopularTVShows(page)
        const newShows = response.data.results
        
        if (page === 1) {
          popularTVShows.value = newShows
        } else {
          popularTVShows.value.push(...newShows)
        }
        
        hasMore.value.popular = page < response.data.total_pages && page < 10 // Limit to 10 pages
        currentPage.value.popular = page
      } catch (error) {
        errors.value.popular = 'Failed to load popular TV shows'
        console.error('Error fetching popular TV shows:', error)
      } finally {
        loading.value.popular = false
        loadingMore.value.popular = false
      }
    }

    const fetchTopRatedTVShows = async (page = 1) => {
      if (page === 1) {
        loading.value.topRated = true
        topRatedTVShows.value = []
      } else {
        loadingMore.value.topRated = true
      }
      
      errors.value.topRated = ''
      
      try {
        const response = await movieService.getTopRatedTVShows(page)
        const newShows = response.data.results
        
        if (page === 1) {
          topRatedTVShows.value = newShows
        } else {
          topRatedTVShows.value.push(...newShows)
        }
        
        hasMore.value.topRated = page < response.data.total_pages && page < 10
        currentPage.value.topRated = page
      } catch (error) {
        errors.value.topRated = 'Failed to load top rated TV shows'
        console.error('Error fetching top rated TV shows:', error)
      } finally {
        loading.value.topRated = false
        loadingMore.value.topRated = false
      }
    }

    // Load more functions
    const loadMorePopular = () => {
      if (hasMore.value.popular && !loadingMore.value.popular) {
        fetchPopularTVShows(currentPage.value.popular + 1)
      }
    }

    const loadMoreTopRated = () => {
      if (hasMore.value.topRated && !loadingMore.value.topRated) {
        fetchTopRatedTVShows(currentPage.value.topRated + 1)
      }
    }

    // Initialize data
    onMounted(() => {
      fetchPopularTVShows()
      fetchTopRatedTVShows()
    })

    return {
      popularTVShows,
      topRatedTVShows,
      loading,
      loadingMore,
      errors,
      hasMore,
      fetchPopularTVShows,
      fetchTopRatedTVShows,
      loadMorePopular,
      loadMoreTopRated
    }
  }
}
</script>

<style scoped>
.tv-shows-view {
  width: 100%;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, var(--primary-bg), var(--secondary-bg));
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  background: linear-gradient(45deg, var(--accent-blue), var(--accent-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.content-sections {
  padding: 2rem 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 2rem 1rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }
}
</style>
