<template>
  <div class="tv-detail-view">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading TV show details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <h2>Error Loading TV Show</h2>
      <p>{{ error }}</p>
      <button class="btn btn-primary" @click="fetchTVDetails">Try Again</button>
    </div>

    <!-- TV Show Details -->
    <div v-else-if="tvShow" class="tv-details">
      <!-- Hero Section -->
      <section class="hero-section" :style="{ backgroundImage: `url(${backdropUrl})` }">
        <div class="hero-overlay">
          <div class="hero-content">
            <div class="tv-poster">
              <img :src="posterUrl" :alt="tvShow.name" class="poster-image" />
            </div>
            <div class="tv-info">
              <h1 class="tv-title">{{ tvShow.name }}</h1>
              <div class="tv-meta">
                <span class="rating">
                  <i class="star-icon">⭐</i>
                  {{ tvShow.vote_average?.toFixed(1) }}
                </span>
                <span class="year">{{ firstAirYear }}</span>
                <span class="seasons" v-if="tvShow.number_of_seasons">
                  {{ tvShow.number_of_seasons }} Season{{ tvShow.number_of_seasons > 1 ? 's' : '' }}
                </span>
              </div>
              <div class="genres" v-if="tvShow.genres && tvShow.genres.length">
                <span v-for="genre in tvShow.genres" :key="genre.id" class="genre-tag">
                  {{ genre.name }}
                </span>
              </div>
              <p class="tv-overview">{{ tvShow.overview }}</p>
              
              <!-- Season and Episode Selection -->
              <div class="episode-selector">
                <div class="selector-group">
                  <label for="season-select">Season:</label>
                  <select id="season-select" v-model="selectedSeason" class="season-select">
                    <option v-for="season in availableSeasons" :key="season" :value="season">
                      Season {{ season }}
                    </option>
                  </select>
                </div>
                <div class="selector-group">
                  <label for="episode-select">Episode:</label>
                  <select id="episode-select" v-model="selectedEpisode" class="episode-select">
                    <option v-for="episode in availableEpisodes" :key="episode" :value="episode">
                      Episode {{ episode }}
                    </option>
                  </select>
                </div>
              </div>
              
              <div class="action-buttons">
                <button class="btn btn-primary" @click="playEpisode">
                  <i class="play-icon">▶</i>
                  Play Episode
                </button>
                <button class="btn btn-secondary" @click="toggleWatchlist">
                  <i class="heart-icon">♡</i>
                  Add to Watchlist
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Player Section -->
      <section v-if="showPlayer" class="player-section">
        <div class="player-container">
          <iframe
            :src="playerUrl"
            class="player-iframe"
            frameborder="0"
            allowfullscreen
            allow="autoplay; encrypted-media"
          ></iframe>
        </div>
      </section>

      <!-- Additional Info -->
      <section class="additional-info">
        <div class="info-container">
          <div class="info-grid">
            <div class="info-item" v-if="tvShow.first_air_date">
              <h3>First Air Date</h3>
              <p>{{ formatDate(tvShow.first_air_date) }}</p>
            </div>
            <div class="info-item" v-if="tvShow.last_air_date">
              <h3>Last Air Date</h3>
              <p>{{ formatDate(tvShow.last_air_date) }}</p>
            </div>
            <div class="info-item" v-if="tvShow.number_of_episodes">
              <h3>Total Episodes</h3>
              <p>{{ tvShow.number_of_episodes }}</p>
            </div>
            <div class="info-item" v-if="tvShow.production_companies && tvShow.production_companies.length">
              <h3>Production Companies</h3>
              <p>{{ tvShow.production_companies.map(c => c.name).join(', ') }}</p>
            </div>
            <div class="info-item" v-if="tvShow.networks && tvShow.networks.length">
              <h3>Networks</h3>
              <p>{{ tvShow.networks.map(n => n.name).join(', ') }}</p>
            </div>
            <div class="info-item" v-if="tvShow.status">
              <h3>Status</h3>
              <p>{{ tvShow.status }}</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { movieService, playerService, imageService } from '@/services/api'

export default {
  name: 'TVDetailView',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const tvShow = ref(null)
    const loading = ref(false)
    const error = ref('')
    const showPlayer = ref(false)
    const selectedSeason = ref(1)
    const selectedEpisode = ref(1)

    const posterUrl = computed(() => {
      if (tvShow.value?.poster_path) {
        return imageService.getPosterUrl(tvShow.value.poster_path, 'w500')
      }
      return 'https://images.unsplash.com/photo-1440404653325-ab127d49abc1?w=500&h=750&fit=crop&crop=center'
    })

    const backdropUrl = computed(() => {
      if (tvShow.value?.backdrop_path) {
        return imageService.getBackdropUrl(tvShow.value.backdrop_path, 'w1280')
      }
      return 'https://images.unsplash.com/photo-1440404653325-ab127d49abc1?w=1280&h=720&fit=crop&crop=center'
    })

    const firstAirYear = computed(() => {
      if (!tvShow.value?.first_air_date) return ''
      return new Date(tvShow.value.first_air_date).getFullYear()
    })

    const availableSeasons = computed(() => {
      if (!tvShow.value?.number_of_seasons) return [1]
      return Array.from({ length: tvShow.value.number_of_seasons }, (_, i) => i + 1)
    })

    const availableEpisodes = computed(() => {
      // Default to 20 episodes per season (could be enhanced with actual episode data)
      const episodeCount = 20
      return Array.from({ length: episodeCount }, (_, i) => i + 1)
    })

    const playerUrl = computed(() => {
      return playerService.getTVPlayerUrl(props.id, selectedSeason.value, selectedEpisode.value)
    })

    const fetchTVDetails = async () => {
      loading.value = true
      error.value = ''
      
      try {
        const response = await movieService.getTVShowDetails(props.id)
        tvShow.value = response.data
      } catch (err) {
        error.value = 'Failed to load TV show details. Please try again.'
        console.error('Error fetching TV show details:', err)
      } finally {
        loading.value = false
      }
    }

    const playEpisode = () => {
      showPlayer.value = true
      // Scroll to player
      setTimeout(() => {
        const playerSection = document.querySelector('.player-section')
        if (playerSection) {
          playerSection.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    }

    const toggleWatchlist = () => {
      // TODO: Implement watchlist functionality
      console.log('Add to watchlist clicked')
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    // Watch for season/episode changes to update player
    watch([selectedSeason, selectedEpisode], () => {
      if (showPlayer.value) {
        // Force iframe reload by toggling showPlayer
        showPlayer.value = false
        setTimeout(() => {
          showPlayer.value = true
        }, 100)
      }
    })

    onMounted(() => {
      fetchTVDetails()
    })

    return {
      tvShow,
      loading,
      error,
      showPlayer,
      selectedSeason,
      selectedEpisode,
      posterUrl,
      backdropUrl,
      firstAirYear,
      availableSeasons,
      availableEpisodes,
      playerUrl,
      fetchTVDetails,
      playEpisode,
      toggleWatchlist,
      formatDate
    }
  }
}
</script>

<style scoped>
.tv-detail-view {
  width: 100%;
  min-height: 100vh;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
}

.hero-section {
  position: relative;
  min-height: 80vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(20, 20, 20, 0.9) 0%,
    rgba(20, 20, 20, 0.6) 50%,
    transparent 100%
  );
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  gap: 3rem;
  width: 100%;
}

.tv-poster {
  flex-shrink: 0;
}

.poster-image {
  width: 300px;
  height: 450px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.6);
}

.tv-info {
  flex: 1;
  max-width: 600px;
}

.tv-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.1;
}

.tv-meta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.rating,
.year,
.seasons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
}

.genres {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.genre-tag {
  background-color: var(--accent-blue);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.tv-overview {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: var(--text-primary);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.episode-selector {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.selector-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.selector-group label {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
}

.season-select,
.episode-select {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  min-width: 120px;
}

.season-select:focus,
.episode-select:focus {
  outline: none;
  border-color: var(--accent-blue);
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.player-section {
  padding: 2rem;
  background-color: var(--primary-bg);
}

.player-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.player-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.additional-info {
  padding: 3rem 2rem;
  background-color: var(--secondary-bg);
}

.info-container {
  max-width: 1200px;
  margin: 0 auto;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.info-item h3 {
  color: var(--accent-blue);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.info-item p {
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
    padding: 1rem;
  }

  .poster-image {
    width: 250px;
    height: 375px;
  }

  .tv-title {
    font-size: 2rem;
  }

  .tv-meta {
    justify-content: center;
  }

  .episode-selector {
    justify-content: center;
  }

  .action-buttons {
    justify-content: center;
  }

  .additional-info {
    padding: 2rem 1rem;
  }
}
</style>
