<template>
  <div class="home-view">
    <!-- Hero Section -->
    <section class="hero-section" :style="{ backgroundImage: `url(${heroBackdrop})` }">
      <div class="hero-overlay">
        <div class="hero-content">
          <h1 class="hero-title">{{ heroContent.title }}</h1>
          <p class="hero-description">{{ heroContent.overview }}</p>
          <div class="hero-actions">
            <button class="btn btn-primary" @click="playHero">
              <i class="play-icon">▶</i>
              Play Now
            </button>
            <button class="btn btn-secondary" @click="viewHeroDetails">
              <i class="info-icon">ℹ</i>
              More Info
            </button>
          </div>
          <div class="hero-meta">
            <span class="rating">
              <i class="star-icon">⭐</i>
              {{ heroContent.vote_average?.toFixed(1) }}
            </span>
            <span class="year">{{ heroYear }}</span>
            <span class="media-type">{{ heroContent.media_type === 'tv' ? 'TV Series' : 'Movie' }}</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Content Sections -->
    <div class="content-sections">
      <ContentSection
        title="Trending Now"
        :items="trendingContent"
        :loading="loading.trending"
        :error="errors.trending"
        media-type="all"
        @retry="fetchTrending"
      />

      <ContentSection
        title="Popular Movies"
        :items="popularMovies"
        :loading="loading.popularMovies"
        :error="errors.popularMovies"
        media-type="movie"
        view-all-route="/movies"
        @retry="fetchPopularMovies"
      />

      <ContentSection
        title="Popular TV Shows"
        :items="popularTVShows"
        :loading="loading.popularTVShows"
        :error="errors.popularTVShows"
        media-type="tv"
        view-all-route="/tv-shows"
        @retry="fetchPopularTVShows"
      />

      <ContentSection
        title="Top Rated Movies"
        :items="topRatedMovies"
        :loading="loading.topRatedMovies"
        :error="errors.topRatedMovies"
        media-type="movie"
        @retry="fetchTopRatedMovies"
      />

      <ContentSection
        title="Top Rated TV Shows"
        :items="topRatedTVShows"
        :loading="loading.topRatedTVShows"
        :error="errors.topRatedTVShows"
        media-type="tv"
        @retry="fetchTopRatedTVShows"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import ContentSection from '@/components/ContentSection.vue'
import { movieService, imageService } from '@/services/api'

export default {
  name: 'HomeView',
  components: {
    ContentSection
  },
  setup() {
    const router = useRouter()

    // Reactive data
    const trendingContent = ref([])
    const popularMovies = ref([])
    const popularTVShows = ref([])
    const topRatedMovies = ref([])
    const topRatedTVShows = ref([])

    // Loading states
    const loading = ref({
      trending: false,
      popularMovies: false,
      popularTVShows: false,
      topRatedMovies: false,
      topRatedTVShows: false
    })

    // Error states
    const errors = ref({
      trending: '',
      popularMovies: '',
      popularTVShows: '',
      topRatedMovies: '',
      topRatedTVShows: ''
    })

    // Hero content (first trending item)
    const heroContent = computed(() => {
      return trendingContent.value[0] || {}
    })

    const heroBackdrop = computed(() => {
      if (heroContent.value.backdrop_path) {
        return imageService.getBackdropUrl(heroContent.value.backdrop_path, 'w1280')
      }
      // Fallback hero background
      return 'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=1280&h=720&fit=crop&crop=center'
    })

    const heroYear = computed(() => {
      const date = heroContent.value.release_date || heroContent.value.first_air_date
      return date ? new Date(date).getFullYear() : ''
    })

    // API calls
    const fetchTrending = async () => {
      loading.value.trending = true
      errors.value.trending = ''
      try {
        const response = await movieService.getTrending('all', 'week')
        trendingContent.value = response.data.results.slice(0, 20)
      } catch (error) {
        errors.value.trending = 'Failed to load trending content'
        console.error('Error fetching trending:', error)
      } finally {
        loading.value.trending = false
      }
    }

    const fetchPopularMovies = async () => {
      loading.value.popularMovies = true
      errors.value.popularMovies = ''
      try {
        const response = await movieService.getPopularMovies()
        popularMovies.value = response.data.results.slice(0, 20)
      } catch (error) {
        errors.value.popularMovies = 'Failed to load popular movies'
        console.error('Error fetching popular movies:', error)
      } finally {
        loading.value.popularMovies = false
      }
    }

    const fetchPopularTVShows = async () => {
      loading.value.popularTVShows = true
      errors.value.popularTVShows = ''
      try {
        const response = await movieService.getPopularTVShows()
        popularTVShows.value = response.data.results.slice(0, 20)
      } catch (error) {
        errors.value.popularTVShows = 'Failed to load popular TV shows'
        console.error('Error fetching popular TV shows:', error)
      } finally {
        loading.value.popularTVShows = false
      }
    }

    const fetchTopRatedMovies = async () => {
      loading.value.topRatedMovies = true
      errors.value.topRatedMovies = ''
      try {
        const response = await movieService.getTopRatedMovies()
        topRatedMovies.value = response.data.results.slice(0, 20)
      } catch (error) {
        errors.value.topRatedMovies = 'Failed to load top rated movies'
        console.error('Error fetching top rated movies:', error)
      } finally {
        loading.value.topRatedMovies = false
      }
    }

    const fetchTopRatedTVShows = async () => {
      loading.value.topRatedTVShows = true
      errors.value.topRatedTVShows = ''
      try {
        const response = await movieService.getTopRatedTVShows()
        topRatedTVShows.value = response.data.results.slice(0, 20)
      } catch (error) {
        errors.value.topRatedTVShows = 'Failed to load top rated TV shows'
        console.error('Error fetching top rated TV shows:', error)
      } finally {
        loading.value.topRatedTVShows = false
      }
    }

    // Hero actions
    const playHero = () => {
      if (heroContent.value.id) {
        const routeName = heroContent.value.media_type === 'tv' ? 'tv-detail' : 'movie-detail'
        router.push({
          name: routeName,
          params: { id: heroContent.value.id }
        })
      }
    }

    const viewHeroDetails = () => {
      playHero() // Same action for now
    }

    // Initialize data
    onMounted(() => {
      fetchTrending()
      fetchPopularMovies()
      fetchPopularTVShows()
      fetchTopRatedMovies()
      fetchTopRatedTVShows()
    })

    return {
      trendingContent,
      popularMovies,
      popularTVShows,
      topRatedMovies,
      topRatedTVShows,
      loading,
      errors,
      heroContent,
      heroBackdrop,
      heroYear,
      fetchTrending,
      fetchPopularMovies,
      fetchPopularTVShows,
      fetchTopRatedMovies,
      fetchTopRatedTVShows,
      playHero,
      viewHeroDetails
    }
  }
}
</script>

<style scoped>
.home-view {
  width: 100%;
}

.hero-section {
  position: relative;
  height: 80vh;
  min-height: 600px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(20, 20, 20, 0.8) 0%,
    rgba(20, 20, 20, 0.4) 50%,
    transparent 100%
  );
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  max-width: 600px;
  line-height: 1.1;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-primary);
  margin-bottom: 2rem;
  max-width: 500px;
  line-height: 1.5;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.hero-meta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  font-size: 1rem;
  color: var(--text-primary);
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.year,
.media-type {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.content-sections {
  padding: 2rem 0;
  background-color: var(--primary-bg);
}

/* Icons */
.play-icon::before { content: '▶'; }
.info-icon::before { content: 'ℹ'; }
.star-icon::before { content: '⭐'; }

/* Mobile Responsive */
@media (max-width: 768px) {
  .hero-section {
    height: 70vh;
    min-height: 500px;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .hero-meta {
    flex-wrap: wrap;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-actions .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
