<script setup>
import { RouterView } from 'vue-router'
import NavBar from './components/NavBar.vue'
</script>

<template>
  <div id="app">
    <NavBar />
    <main class="main-content">
      <RouterView />
    </main>
  </div>
</template>

<style>
.main-content {
  padding-top: 80px; /* Account for fixed navbar */
  min-height: 100vh;
}

/* Global transition for route changes */
.router-view {
  transition: all 0.3s ease;
}
</style>
