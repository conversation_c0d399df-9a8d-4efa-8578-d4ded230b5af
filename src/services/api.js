import axios from 'axios'

// Using a demo TMDB API key - in production, you should use your own API key
const API_KEY = 'demo_key' // Replace with your actual TMDB API key
const BASE_URL = 'https://api.themoviedb.org/3'

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  params: {
    api_key: API_KEY
  }
})

// Mock data for demo purposes when API key is not available
const mockMovies = [
  {
    id: 1,
    title: "The Shawshank Redemption",
    overview: "Two imprisoned men bond over a number of years, finding solace and eventual redemption through acts of common decency.",
    poster_path: "/placeholder-poster.jpg",
    backdrop_path: "/placeholder-backdrop.jpg",
    vote_average: 9.3,
    release_date: "1994-09-23",
    media_type: "movie",
    genres: [{ id: 18, name: "Drama" }]
  },
  {
    id: 2,
    title: "The Godfather",
    overview: "The aging patriarch of an organized crime dynasty transfers control of his clandestine empire to his reluctant son.",
    poster_path: "/placeholder-poster.jpg",
    backdrop_path: "/placeholder-backdrop.jpg",
    vote_average: 9.2,
    release_date: "1972-03-24",
    media_type: "movie",
    genres: [{ id: 18, name: "Drama" }, { id: 80, name: "Crime" }]
  },
  {
    id: 3,
    title: "The Dark Knight",
    overview: "When the menace known as the Joker wreaks havoc and chaos on the people of Gotham, Batman must accept one of the greatest psychological and physical tests.",
    poster_path: "/placeholder-poster.jpg",
    backdrop_path: "/placeholder-backdrop.jpg",
    vote_average: 9.0,
    release_date: "2008-07-18",
    media_type: "movie",
    genres: [{ id: 28, name: "Action" }, { id: 80, name: "Crime" }]
  }
]

const mockTVShows = [
  {
    id: 1,
    name: "Breaking Bad",
    overview: "A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine.",
    poster_path: "/placeholder-poster.jpg",
    backdrop_path: "/placeholder-backdrop.jpg",
    vote_average: 9.5,
    first_air_date: "2008-01-20",
    media_type: "tv",
    genres: [{ id: 18, name: "Drama" }, { id: 80, name: "Crime" }]
  },
  {
    id: 2,
    name: "Game of Thrones",
    overview: "Nine noble families fight for control over the lands of Westeros, while an ancient enemy returns after being dormant for millennia.",
    poster_path: "/placeholder-poster.jpg",
    backdrop_path: "/placeholder-backdrop.jpg",
    vote_average: 9.3,
    first_air_date: "2011-04-17",
    media_type: "tv",
    genres: [{ id: 18, name: "Drama" }, { id: 10759, name: "Action & Adventure" }]
  }
]

// Helper function to create mock response
const createMockResponse = (results, page = 1) => ({
  data: {
    results,
    page,
    total_pages: 5,
    total_results: results.length * 5
  }
})

// Movie and TV show data service
export const movieService = {
  // Get trending movies and TV shows
  getTrending: async (mediaType = 'all', timeWindow = 'week') => {
    try {
      return await api.get(`/trending/${mediaType}/${timeWindow}`)
    } catch (error) {
      console.warn('Using mock data for trending content')
      return createMockResponse([...mockMovies, ...mockTVShows])
    }
  },

  // Get popular movies
  getPopularMovies: async (page = 1) => {
    try {
      return await api.get('/movie/popular', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for popular movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get popular TV shows
  getPopularTVShows: async (page = 1) => {
    try {
      return await api.get('/tv/popular', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for popular TV shows')
      return createMockResponse(mockTVShows, page)
    }
  },

  // Get top rated movies
  getTopRatedMovies: async (page = 1) => {
    try {
      return await api.get('/movie/top_rated', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for top rated movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get top rated TV shows
  getTopRatedTVShows: async (page = 1) => {
    try {
      return await api.get('/tv/top_rated', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for top rated TV shows')
      return createMockResponse(mockTVShows, page)
    }
  },

  // Get now playing movies
  getNowPlayingMovies: async (page = 1) => {
    try {
      return await api.get('/movie/now_playing', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for now playing movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get upcoming movies
  getUpcomingMovies: async (page = 1) => {
    try {
      return await api.get('/movie/upcoming', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for upcoming movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get movie details
  getMovieDetails: async (movieId) => {
    try {
      return await api.get(`/movie/${movieId}`)
    } catch (error) {
      console.warn('Using mock data for movie details')
      const movie = mockMovies.find(m => m.id == movieId) || mockMovies[0]
      return { data: { ...movie, runtime: 142, budget: 25000000, revenue: 16000000 } }
    }
  },

  // Get TV show details
  getTVShowDetails: async (tvId) => {
    try {
      return await api.get(`/tv/${tvId}`)
    } catch (error) {
      console.warn('Using mock data for TV show details')
      const show = mockTVShows.find(s => s.id == tvId) || mockTVShows[0]
      return {
        data: {
          ...show,
          number_of_seasons: 5,
          number_of_episodes: 62,
          status: 'Ended',
          networks: [{ name: 'AMC' }],
          production_companies: [{ name: 'Sony Pictures Television' }]
        }
      }
    }
  },

  // Search movies
  searchMovies: async (query, page = 1) => {
    try {
      return await api.get('/search/movie', { params: { query, page } })
    } catch (error) {
      console.warn('Using mock data for movie search')
      const filtered = mockMovies.filter(m =>
        m.title.toLowerCase().includes(query.toLowerCase())
      )
      return createMockResponse(filtered, page)
    }
  },

  // Search TV shows
  searchTVShows: async (query, page = 1) => {
    try {
      return await api.get('/search/tv', { params: { query, page } })
    } catch (error) {
      console.warn('Using mock data for TV show search')
      const filtered = mockTVShows.filter(s =>
        s.name.toLowerCase().includes(query.toLowerCase())
      )
      return createMockResponse(filtered, page)
    }
  },

  // Get movie genres
  getMovieGenres: async () => {
    try {
      return await api.get('/genre/movie/list')
    } catch (error) {
      return { data: { genres: [
        { id: 28, name: "Action" },
        { id: 18, name: "Drama" },
        { id: 80, name: "Crime" }
      ]}}
    }
  },

  // Get TV genres
  getTVGenres: async () => {
    try {
      return await api.get('/genre/tv/list')
    } catch (error) {
      return { data: { genres: [
        { id: 18, name: "Drama" },
        { id: 10759, name: "Action & Adventure" },
        { id: 80, name: "Crime" }
      ]}}
    }
  }
}

// Player service for embedding
export const playerService = {
  // Get movie player URL
  getMoviePlayerUrl: (movieId) => {
    return `https://player.autoembed.cc/embed/movie/${movieId}`
  },

  // Get TV show player URL
  getTVPlayerUrl: (tvId, season, episode) => {
    return `https://player.autoembed.cc/embed/tv/${tvId}/${season}/${episode}`
  }
}

// Image service for TMDB images
export const imageService = {
  // Get full image URL
  getImageUrl: (path, size = 'w500') => {
    if (!path || path.includes('placeholder')) {
      return `https://via.placeholder.com/500x750/333333/ffffff?text=No+Image`
    }
    return `https://image.tmdb.org/t/p/${size}${path}`
  },

  // Get backdrop URL
  getBackdropUrl: (path, size = 'w1280') => {
    if (!path || path.includes('placeholder')) {
      return `https://via.placeholder.com/1280x720/333333/ffffff?text=No+Backdrop`
    }
    return `https://image.tmdb.org/t/p/${size}${path}`
  },

  // Get poster URL
  getPosterUrl: (path, size = 'w500') => {
    if (!path || path.includes('placeholder')) {
      return `https://via.placeholder.com/500x750/333333/ffffff?text=No+Poster`
    }
    return `https://image.tmdb.org/t/p/${size}${path}`
  }
}

export default { movieService, playerService, imageService }
