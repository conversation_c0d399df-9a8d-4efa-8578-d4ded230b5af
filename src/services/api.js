import axios from 'axios'

// Using a demo TMDB API key - in production, you should use your own API key
const API_KEY = 'demo_key' // Replace with your actual TMDB API key
const BASE_URL = 'https://api.themoviedb.org/3'

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  params: {
    api_key: API_KEY
  }
})

// Mock data for demo purposes when API key is not available
const mockMovies = [
  {
    id: 1,
    title: "The Shawshank Redemption",
    overview: "Two imprisoned men bond over a number of years, finding solace and eventual redemption through acts of common decency.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 9.3,
    release_date: "1994-09-23",
    media_type: "movie",
    genres: [{ id: 18, name: "Drama" }]
  },
  {
    id: 2,
    title: "The Godfather",
    overview: "The aging patriarch of an organized crime dynasty transfers control of his clandestine empire to his reluctant son.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 9.2,
    release_date: "1972-03-24",
    media_type: "movie",
    genres: [{ id: 18, name: "Drama" }, { id: 80, name: "Crime" }]
  },
  {
    id: 3,
    title: "The Dark Knight",
    overview: "When the menace known as the Joker wreaks havoc and chaos on the people of Gotham, Batman must accept one of the greatest psychological and physical tests.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 9.0,
    release_date: "2008-07-18",
    media_type: "movie",
    genres: [{ id: 28, name: "Action" }, { id: 80, name: "Crime" }]
  },
  {
    id: 4,
    title: "Pulp Fiction",
    overview: "The lives of two mob hitmen, a boxer, a gangster and his wife, and a pair of diner bandits intertwine in four tales of violence and redemption.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 8.9,
    release_date: "1994-10-14",
    media_type: "movie",
    genres: [{ id: 18, name: "Drama" }, { id: 80, name: "Crime" }]
  },
  {
    id: 5,
    title: "Forrest Gump",
    overview: "The presidencies of Kennedy and Johnson, the Vietnam War, the Watergate scandal and other historical events unfold from the perspective of an Alabama man.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 8.8,
    release_date: "1994-06-23",
    media_type: "movie",
    genres: [{ id: 18, name: "Drama" }, { id: 35, name: "Comedy" }]
  }
]

const mockTVShows = [
  {
    id: 1,
    name: "Breaking Bad",
    overview: "A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 9.5,
    first_air_date: "2008-01-20",
    media_type: "tv",
    genres: [{ id: 18, name: "Drama" }, { id: 80, name: "Crime" }]
  },
  {
    id: 2,
    name: "Game of Thrones",
    overview: "Nine noble families fight for control over the lands of Westeros, while an ancient enemy returns after being dormant for millennia.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 9.3,
    first_air_date: "2011-04-17",
    media_type: "tv",
    genres: [{ id: 18, name: "Drama" }, { id: 10759, name: "Action & Adventure" }]
  },
  {
    id: 3,
    name: "The Sopranos",
    overview: "New Jersey mob boss Tony Soprano deals with personal and professional issues in his home and business life.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 9.2,
    first_air_date: "1999-01-10",
    media_type: "tv",
    genres: [{ id: 18, name: "Drama" }, { id: 80, name: "Crime" }]
  },
  {
    id: 4,
    name: "The Wire",
    overview: "The Baltimore drug scene, as seen through the eyes of drug dealers and law enforcement.",
    poster_path: null,
    backdrop_path: null,
    vote_average: 9.3,
    first_air_date: "2002-06-02",
    media_type: "tv",
    genres: [{ id: 18, name: "Drama" }, { id: 80, name: "Crime" }]
  }
]

// Helper function to create mock response
const createMockResponse = (results, page = 1) => ({
  data: {
    results,
    page,
    total_pages: 5,
    total_results: results.length * 5
  }
})

// Movie and TV show data service
export const movieService = {
  // Get trending movies and TV shows
  getTrending: async (mediaType = 'all', timeWindow = 'week') => {
    try {
      return await api.get(`/trending/${mediaType}/${timeWindow}`)
    } catch (error) {
      console.warn('Using mock data for trending content')
      return createMockResponse([...mockMovies, ...mockTVShows])
    }
  },

  // Get popular movies
  getPopularMovies: async (page = 1) => {
    try {
      return await api.get('/movie/popular', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for popular movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get popular TV shows
  getPopularTVShows: async (page = 1) => {
    try {
      return await api.get('/tv/popular', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for popular TV shows')
      return createMockResponse(mockTVShows, page)
    }
  },

  // Get top rated movies
  getTopRatedMovies: async (page = 1) => {
    try {
      return await api.get('/movie/top_rated', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for top rated movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get top rated TV shows
  getTopRatedTVShows: async (page = 1) => {
    try {
      return await api.get('/tv/top_rated', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for top rated TV shows')
      return createMockResponse(mockTVShows, page)
    }
  },

  // Get now playing movies
  getNowPlayingMovies: async (page = 1) => {
    try {
      return await api.get('/movie/now_playing', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for now playing movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get upcoming movies
  getUpcomingMovies: async (page = 1) => {
    try {
      return await api.get('/movie/upcoming', { params: { page } })
    } catch (error) {
      console.warn('Using mock data for upcoming movies')
      return createMockResponse(mockMovies, page)
    }
  },

  // Get movie details
  getMovieDetails: async (movieId) => {
    try {
      return await api.get(`/movie/${movieId}`)
    } catch (error) {
      console.warn('Using mock data for movie details')
      const movie = mockMovies.find(m => m.id == movieId) || mockMovies[0]
      return { data: { ...movie, runtime: 142, budget: 25000000, revenue: 16000000 } }
    }
  },

  // Get TV show details
  getTVShowDetails: async (tvId) => {
    try {
      return await api.get(`/tv/${tvId}`)
    } catch (error) {
      console.warn('Using mock data for TV show details')
      const show = mockTVShows.find(s => s.id == tvId) || mockTVShows[0]
      return {
        data: {
          ...show,
          number_of_seasons: 5,
          number_of_episodes: 62,
          status: 'Ended',
          networks: [{ name: 'AMC' }],
          production_companies: [{ name: 'Sony Pictures Television' }]
        }
      }
    }
  },

  // Search movies
  searchMovies: async (query, page = 1) => {
    try {
      return await api.get('/search/movie', { params: { query, page } })
    } catch (error) {
      console.warn('Using mock data for movie search')
      const filtered = mockMovies.filter(m =>
        m.title.toLowerCase().includes(query.toLowerCase())
      )
      return createMockResponse(filtered, page)
    }
  },

  // Search TV shows
  searchTVShows: async (query, page = 1) => {
    try {
      return await api.get('/search/tv', { params: { query, page } })
    } catch (error) {
      console.warn('Using mock data for TV show search')
      const filtered = mockTVShows.filter(s =>
        s.name.toLowerCase().includes(query.toLowerCase())
      )
      return createMockResponse(filtered, page)
    }
  },

  // Get movie genres
  getMovieGenres: async () => {
    try {
      return await api.get('/genre/movie/list')
    } catch (error) {
      return { data: { genres: [
        { id: 28, name: "Action" },
        { id: 18, name: "Drama" },
        { id: 80, name: "Crime" }
      ]}}
    }
  },

  // Get TV genres
  getTVGenres: async () => {
    try {
      return await api.get('/genre/tv/list')
    } catch (error) {
      return { data: { genres: [
        { id: 18, name: "Drama" },
        { id: 10759, name: "Action & Adventure" },
        { id: 80, name: "Crime" }
      ]}}
    }
  }
}

// Player service for embedding
export const playerService = {
  // Get movie player URL
  getMoviePlayerUrl: (movieId) => {
    return `https://player.autoembed.cc/embed/movie/${movieId}`
  },

  // Get TV show player URL
  getTVPlayerUrl: (tvId, season, episode) => {
    return `https://player.autoembed.cc/embed/tv/${tvId}/${season}/${episode}`
  }
}

// Image service for TMDB images
export const imageService = {
  // Get full image URL
  getImageUrl: (path, size = 'w500') => {
    if (!path || path.includes('placeholder')) {
      return `https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=500&h=750&fit=crop&crop=center`
    }
    return `https://image.tmdb.org/t/p/${size}${path}`
  },

  // Get backdrop URL
  getBackdropUrl: (path, size = 'w1280') => {
    if (!path || path.includes('placeholder')) {
      return `https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=1280&h=720&fit=crop&crop=center`
    }
    return `https://image.tmdb.org/t/p/${size}${path}`
  },

  // Get poster URL
  getPosterUrl: (path, size = 'w500') => {
    if (!path || path.includes('placeholder')) {
      // Use different movie-themed images for variety
      const movieImages = [
        'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=500&h=750&fit=crop&crop=center', // Cinema
        'https://images.unsplash.com/photo-1440404653325-ab127d49abc1?w=500&h=750&fit=crop&crop=center', // Movie theater
        'https://images.unsplash.com/photo-1518676590629-3dcbd9c5a5c9?w=500&h=750&fit=crop&crop=center', // Film reel
        'https://images.unsplash.com/photo-1536440136628-849c177e76a1?w=500&h=750&fit=crop&crop=center', // Movie camera
        'https://images.unsplash.com/photo-1594909122845-11baa439b7bf?w=500&h=750&fit=crop&crop=center'  // Popcorn
      ]
      const randomIndex = Math.floor(Math.random() * movieImages.length)
      return movieImages[randomIndex]
    }
    return `https://image.tmdb.org/t/p/${size}${path}`
  }
}

export default { movieService, playerService, imageService }
