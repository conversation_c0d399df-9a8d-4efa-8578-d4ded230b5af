<template>
  <section class="content-section">
    <div class="section-header">
      <h2 class="section-title">{{ title }}</h2>
      <button 
        v-if="showViewAll && items.length > 0" 
        class="view-all-btn"
        @click="handleViewAll"
      >
        View All
        <i class="arrow-icon">→</i>
      </button>
    </div>

    <div class="section-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <div class="spinner"></div>
        <p>Loading {{ title.toLowerCase() }}...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <p class="error-message">{{ error }}</p>
        <button class="btn btn-secondary" @click="$emit('retry')">
          Try Again
        </button>
      </div>

      <!-- Content Grid/Carousel -->
      <div v-else-if="items.length > 0" class="content-container">
        <!-- Horizontal Scroll for carousel view -->
        <div v-if="viewType === 'carousel'" class="content-carousel" ref="carousel">
          <div class="carousel-track" :style="{ transform: `translateX(${scrollOffset}px)` }">
            <MovieCard
              v-for="item in items"
              :key="item.id"
              :id="item.id"
              :title="item.title || item.name"
              :overview="item.overview"
              :poster-path="item.poster_path"
              :backdrop-path="item.backdrop_path"
              :rating="item.vote_average"
              :release-date="item.release_date || item.first_air_date"
              :media-type="item.media_type || mediaType"
              :genres="item.genres || []"
            />
          </div>
          
          <!-- Navigation Arrows -->
          <button 
            v-if="canScrollLeft" 
            class="carousel-nav carousel-nav-left"
            @click="scrollLeft"
          >
            ‹
          </button>
          <button 
            v-if="canScrollRight" 
            class="carousel-nav carousel-nav-right"
            @click="scrollRight"
          >
            ›
          </button>
        </div>

        <!-- Grid view for view all pages -->
        <div v-else class="content-grid">
          <MovieCard
            v-for="item in items"
            :key="item.id"
            :id="item.id"
            :title="item.title || item.name"
            :overview="item.overview"
            :poster-path="item.poster_path"
            :backdrop-path="item.backdrop_path"
            :rating="item.vote_average"
            :release-date="item.release_date || item.first_air_date"
            :media-type="item.media_type || mediaType"
            :genres="item.genres || []"
          />
        </div>

        <!-- Load More Button for grid view -->
        <div v-if="viewType === 'grid' && hasMore" class="load-more-container">
          <button 
            class="btn btn-secondary load-more-btn"
            @click="$emit('load-more')"
            :disabled="loadingMore"
          >
            <span v-if="loadingMore" class="spinner small"></span>
            {{ loadingMore ? 'Loading...' : 'Load More' }}
          </button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="empty-container">
        <p class="empty-message">No {{ title.toLowerCase() }} found</p>
      </div>
    </div>
  </section>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import MovieCard from './MovieCard.vue'

export default {
  name: 'ContentSection',
  components: {
    MovieCard
  },
  props: {
    title: {
      type: String,
      required: true
    },
    items: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    showViewAll: {
      type: Boolean,
      default: true
    },
    viewType: {
      type: String,
      default: 'carousel',
      validator: (value) => ['carousel', 'grid'].includes(value)
    },
    mediaType: {
      type: String,
      default: 'movie'
    },
    viewAllRoute: {
      type: String,
      default: ''
    },
    hasMore: {
      type: Boolean,
      default: false
    },
    loadingMore: {
      type: Boolean,
      default: false
    }
  },
  emits: ['view-all', 'retry', 'load-more'],
  setup(props, { emit }) {
    const router = useRouter()
    const carousel = ref(null)
    const scrollOffset = ref(0)
    const cardWidth = 220 // Card width + gap
    const visibleCards = ref(6)

    const canScrollLeft = computed(() => scrollOffset.value < 0)
    const canScrollRight = computed(() => {
      const maxScroll = -(props.items.length - visibleCards.value) * cardWidth
      return scrollOffset.value > maxScroll && props.items.length > visibleCards.value
    })

    const updateVisibleCards = () => {
      if (typeof window !== 'undefined') {
        const width = window.innerWidth
        if (width < 768) {
          visibleCards.value = 2
        } else if (width < 1024) {
          visibleCards.value = 3
        } else if (width < 1400) {
          visibleCards.value = 5
        } else {
          visibleCards.value = 6
        }
      }
    }

    const scrollLeft = () => {
      scrollOffset.value = Math.min(scrollOffset.value + cardWidth * 2, 0)
    }

    const scrollRight = () => {
      const maxScroll = -(props.items.length - visibleCards.value) * cardWidth
      scrollOffset.value = Math.max(scrollOffset.value - cardWidth * 2, maxScroll)
    }

    const handleViewAll = () => {
      if (props.viewAllRoute) {
        router.push(props.viewAllRoute)
      } else {
        emit('view-all')
      }
    }

    onMounted(() => {
      updateVisibleCards()
      window.addEventListener('resize', updateVisibleCards)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', updateVisibleCards)
    })

    return {
      carousel,
      scrollOffset,
      canScrollLeft,
      canScrollRight,
      scrollLeft,
      scrollRight,
      handleViewAll
    }
  }
}
</script>

<style scoped>
.content-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0 2rem;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.view-all-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

.view-all-btn:hover {
  color: var(--accent-red);
  background-color: var(--hover-bg);
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.view-all-btn:hover .arrow-icon {
  transform: translateX(4px);
}

.section-content {
  position: relative;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
}

.error-message,
.empty-message {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.content-container {
  position: relative;
}

.content-carousel {
  position: relative;
  overflow: hidden;
  padding: 0 2rem;
}

.carousel-track {
  display: flex;
  gap: 1rem;
  transition: transform 0.3s ease;
  padding: 1rem 0;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  padding: 0 2rem;
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
  font-size: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
}

.carousel-nav:hover {
  background-color: var(--accent-red);
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav-left {
  left: 0.5rem;
}

.carousel-nav-right {
  right: 0.5rem;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  padding: 0 2rem;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .section-header {
    padding: 0 1rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .content-carousel {
    padding: 0 1rem;
  }

  .content-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    padding: 0 1rem;
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .load-more-container {
    padding: 0 1rem;
  }
}
</style>
