<template>
  <div class="movie-card" @click="handleClick">
    <div class="card-image-container">
      <img
        :src="currentImageUrl"
        :alt="title"
        class="card-image"
        @load="imageLoaded = true"
        @error="handleImageError"
      />
      <div v-if="!imageLoaded" class="image-placeholder">
        <div class="spinner"></div>
      </div>
      
      <!-- Overlay with play button -->
      <div class="card-overlay">
        <div class="play-button">
          <i class="play-icon">▶</i>
        </div>
        <div class="card-info">
          <div class="rating" v-if="rating">
            <span class="star">⭐</span>
            {{ rating.toFixed(1) }}
          </div>
          <div class="year" v-if="year">{{ year }}</div>
        </div>
      </div>
    </div>
    
    <div class="card-content">
      <h3 class="card-title">{{ title }}</h3>
      <p class="card-description" v-if="overview">{{ truncatedOverview }}</p>
      <div class="card-meta">
        <span class="media-type">{{ mediaType === 'tv' ? 'TV Show' : 'Movie' }}</span>
        <div class="genres" v-if="genres && genres.length">
          <span v-for="genre in limitedGenres" :key="genre.id" class="genre-tag">
            {{ genre.name }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { imageService } from '@/services/api'

export default {
  name: 'MovieCard',
  props: {
    id: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    overview: {
      type: String,
      default: ''
    },
    posterPath: {
      type: String,
      default: ''
    },
    backdropPath: {
      type: String,
      default: ''
    },
    rating: {
      type: Number,
      default: 0
    },
    releaseDate: {
      type: String,
      default: ''
    },
    mediaType: {
      type: String,
      default: 'movie',
      validator: (value) => ['movie', 'tv'].includes(value)
    },
    genres: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const router = useRouter()
    const imageLoaded = ref(false)
    const imageError = ref(false)

    const posterUrl = computed(() => {
      return imageService.getPosterUrl(props.posterPath, 'w342')
    })

    const fallbackImageUrl = computed(() => {
      // Movie-themed fallback images
      const fallbackImages = [
        'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=342&h=513&fit=crop&crop=center',
        'https://images.unsplash.com/photo-1440404653325-ab127d49abc1?w=342&h=513&fit=crop&crop=center',
        'https://images.unsplash.com/photo-1518676590629-3dcbd9c5a5c9?w=342&h=513&fit=crop&crop=center',
        'https://images.unsplash.com/photo-1536440136628-849c177e76a1?w=342&h=513&fit=crop&crop=center',
        'https://images.unsplash.com/photo-1594909122845-11baa439b7bf?w=342&h=513&fit=crop&crop=center'
      ]
      const index = props.id % fallbackImages.length
      return fallbackImages[index]
    })

    const currentImageUrl = computed(() => {
      if (imageError.value) {
        return fallbackImageUrl.value
      }
      return posterUrl.value
    })

    const year = computed(() => {
      if (!props.releaseDate) return ''
      return new Date(props.releaseDate).getFullYear()
    })

    const truncatedOverview = computed(() => {
      if (!props.overview) return ''
      return props.overview.length > 120
        ? props.overview.substring(0, 120) + '...'
        : props.overview
    })

    const limitedGenres = computed(() => {
      return props.genres.slice(0, 2)
    })

    const handleClick = () => {
      const routeName = props.mediaType === 'tv' ? 'tv-detail' : 'movie-detail'
      router.push({
        name: routeName,
        params: { id: props.id }
      })
    }

    const handleImageError = () => {
      console.log('Image failed to load, using fallback')
      imageError.value = true
      imageLoaded.value = true
    }

    return {
      imageLoaded,
      imageError,
      posterUrl,
      fallbackImageUrl,
      currentImageUrl,
      year,
      truncatedOverview,
      limitedGenres,
      handleClick,
      handleImageError
    }
  }
}
</script>

<style scoped>
.movie-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-width: 200px;
  max-width: 250px;
}

.movie-card:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}

.card-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 2/3;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.movie-card:hover .card-image {
  transform: scale(1.1);
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.image-placeholder::before {
  content: '🎬';
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    transparent 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.movie-card:hover .card-overlay {
  opacity: 1;
}

.play-button {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
  margin-bottom: auto;
  transition: all 0.3s ease;
  transform: scale(0.8);
}

.movie-card:hover .play-button {
  transform: scale(1);
  background-color: var(--accent-red);
}

.play-icon {
  color: var(--primary-bg);
  font-size: 1.5rem;
  margin-left: 4px;
}

.movie-card:hover .play-icon {
  color: white;
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: auto;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
}

.star {
  font-size: 0.8rem;
}

.year {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
}

.card-content {
  padding: 1rem;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.card-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.media-type {
  color: var(--accent-red);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.genres {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.genre-tag {
  background-color: var(--hover-bg);
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.75rem;
  border: 1px solid var(--border-color);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .movie-card {
    min-width: 150px;
    max-width: 180px;
  }

  .card-title {
    font-size: 1rem;
  }

  .card-description {
    font-size: 0.8rem;
  }

  .play-button {
    width: 50px;
    height: 50px;
  }

  .play-icon {
    font-size: 1.2rem;
  }
}
</style>
