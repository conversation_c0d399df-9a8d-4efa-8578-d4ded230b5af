<template>
  <nav class="navbar" :class="{ 'navbar-scrolled': isScrolled }">
    <div class="navbar-container">
      <!-- Logo -->
      <div class="navbar-brand">
        <router-link to="/" class="brand-link">
          <h1 class="brand-text">MovieStream</h1>
        </router-link>
      </div>

      <!-- Navigation Links -->
      <div class="navbar-nav" :class="{ 'nav-open': isMobileMenuOpen }">
        <router-link to="/" class="nav-link" @click="closeMobileMenu">
          <i class="icon-home"></i>
          Home
        </router-link>
        <router-link to="/movies" class="nav-link" @click="closeMobileMenu">
          <i class="icon-movie"></i>
          Movies
        </router-link>
        <router-link to="/tv-shows" class="nav-link" @click="closeMobileMenu">
          <i class="icon-tv"></i>
          TV Shows
        </router-link>
      </div>

      <!-- Search and Mobile Menu -->
      <div class="navbar-actions">
        <!-- Search -->
        <div class="search-container" :class="{ 'search-active': isSearchActive }">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search movies, TV shows..."
            class="search-input"
            @focus="isSearchActive = true"
            @blur="handleSearchBlur"
            @keyup.enter="performSearch"
          />
          <button class="search-btn" @click="toggleSearch">
            <i class="icon-search"></i>
          </button>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-btn" @click="toggleMobileMenu">
          <span class="hamburger-line" :class="{ 'active': isMobileMenuOpen }"></span>
          <span class="hamburger-line" :class="{ 'active': isMobileMenuOpen }"></span>
          <span class="hamburger-line" :class="{ 'active': isMobileMenuOpen }"></span>
        </button>
      </div>
    </div>
  </nav>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'NavBar',
  setup() {
    const router = useRouter()
    const isScrolled = ref(false)
    const isMobileMenuOpen = ref(false)
    const isSearchActive = ref(false)
    const searchQuery = ref('')

    const handleScroll = () => {
      isScrolled.value = window.scrollY > 50
    }

    const toggleMobileMenu = () => {
      isMobileMenuOpen.value = !isMobileMenuOpen.value
    }

    const closeMobileMenu = () => {
      isMobileMenuOpen.value = false
    }

    const toggleSearch = () => {
      isSearchActive.value = !isSearchActive.value
      if (isSearchActive.value) {
        // Focus search input after animation
        setTimeout(() => {
          const searchInput = document.querySelector('.search-input')
          if (searchInput) searchInput.focus()
        }, 300)
      }
    }

    const handleSearchBlur = () => {
      if (!searchQuery.value) {
        setTimeout(() => {
          isSearchActive.value = false
        }, 200)
      }
    }

    const performSearch = () => {
      if (searchQuery.value.trim()) {
        router.push(`/search?q=${encodeURIComponent(searchQuery.value)}`)
        isSearchActive.value = false
        closeMobileMenu()
      }
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      isScrolled,
      isMobileMenuOpen,
      isSearchActive,
      searchQuery,
      toggleMobileMenu,
      closeMobileMenu,
      toggleSearch,
      handleSearchBlur,
      performSearch
    }
  }
}
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(180deg, rgba(20, 20, 20, 0.9) 0%, transparent 100%);
  transition: all 0.3s ease;
  padding: 0;
}

.navbar-scrolled {
  background-color: rgba(20, 20, 20, 0.95);
  backdrop-filter: blur(10px);
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
}

.navbar-brand .brand-link {
  text-decoration: none;
  color: var(--accent-red);
}

.brand-text {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, var(--accent-red), var(--accent-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--accent-red);
  background-color: var(--hover-bg);
}

.nav-link.router-link-active {
  color: var(--accent-red);
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: var(--accent-red);
  border-radius: 50%;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 8px 40px 8px 16px;
  color: var(--text-primary);
  font-size: 14px;
  width: 0;
  opacity: 0;
  transition: all 0.3s ease;
}

.search-container.search-active .search-input {
  width: 250px;
  opacity: 1;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-red);
  background: rgba(255, 255, 255, 0.15);
}

.search-btn {
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: absolute;
  right: 0;
}

.search-btn:hover {
  color: var(--accent-red);
  background-color: var(--hover-bg);
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-line.active:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-line.active:nth-child(2) {
  opacity: 0;
}

.hamburger-line.active:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Icons */
.icon-home::before { content: '🏠'; }
.icon-movie::before { content: '🎬'; }
.icon-tv::before { content: '📺'; }
.icon-search::before { content: '🔍'; }

/* Mobile Styles */
@media (max-width: 768px) {
  .navbar-container {
    padding: 1rem;
  }

  .brand-text {
    font-size: 1.5rem;
  }

  .navbar-nav {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background-color: rgba(20, 20, 20, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    padding: 2rem;
    gap: 1rem;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .navbar-nav.nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .search-container.search-active .search-input {
    width: 200px;
  }
}
</style>
